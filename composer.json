{"name": "drupal/recommended-project", "description": "Project template for Drupal projects with a relocated document root", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "composer", "url": "https://asset-packagist.org"}], "require": {"composer/installers": "^2.0", "drupal/admin_toolbar": "^3.6", "drupal/administerusersbyrole": "^3.5", "drupal/better_exposed_filters": "^7.0", "drupal/computed_field": "^3.0", "drupal/config_ignore": "^3.3", "drupal/core-composer-scaffold": "^10.4", "drupal/core-project-message": "^10.4", "drupal/core-recommended": "^10.4", "drupal/default_content_deploy": "^2.1", "drupal/devel": "^5.3", "drupal/devel_entity_updates": "^4.1", "drupal/entity_clone": "^2.1@beta", "drupal/entity_form_mode": "^2.0", "drupal/field_group": "^4.0", "drupal/flood_control": "^3.0", "drupal/gin": "^4.0", "drupal/gin_login": "^2.1", "drupal/gin_toolbar": "^2.0", "drupal/inline_entity_form": "^1.0@RC", "drupal/paragraphs": "^1.19", "drupal/password_policy": "^4.0", "drupal/pathauto": "^1.13", "drupal/quiz": "^7.0", "drupal/recaptcha_v3": "^2.0", "drupal/redirect": "^1.11", "drupal/remove_http_headers": "^2.1", "drupal/rules": "^4.0", "drupal/select2": "^2.0", "drupal/theme_switcher": "^2.1", "drupal/twig_tweak": "^3.4", "drupal/views_bulk_operations": "^4.3", "drupal/views_data_export": "^1.6", "drupal/webform": "^6.2", "drupal/webform_workflows_element": "^1.0@alpha", "drupal/xls_serialization": "^2.0", "drush/drush": "^13.6", "google/cloud-storage": "^1.48", "npm-asset/select2": "^4.0", "oomphinc/composer-installers-extender": "^2.0", "phpoffice/phpspreadsheet": "^2.3.7"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "dealerdirect/phpcodesniffer-composer-installer": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpstan/extension-installer": true}, "sort-packages": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library", "type:bower-asset", "type:npm-asset"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"]}, "installer-types": ["bower-asset", "npm-asset"], "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}}