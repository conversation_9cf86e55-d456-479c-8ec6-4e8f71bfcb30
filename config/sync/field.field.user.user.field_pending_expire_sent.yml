uuid: ef434c2a-5bc6-47c6-91dd-057a602cd5d7
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_pending_expire_sent
  module:
    - user
  enforced:
    module:
      - password_policy
_core:
  default_config_hash: oYe3JWMO6vLsJ9iypb1Bg9YQcT3TGqGtKwzHc3jcTXg
id: user.user.field_pending_expire_sent
field_name: field_pending_expire_sent
entity_type: user
bundle: user
label: 'Pending Expiration Mail Count'
description: 'Whether an email notifying of a pending password expiration has been sent'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  min: 0
  max: null
  prefix: ''
  suffix: ''
field_type: integer
