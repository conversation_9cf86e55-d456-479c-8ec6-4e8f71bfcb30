uuid: 93d7b4c6-e7bd-49fe-9f14-88ad45c67d02
langcode: en
status: true
dependencies:
  config:
    - field.storage.quiz_question.field_question_category
    - field.storage.quiz_result_answer.field_auditor_comment
    - field.storage.quiz_result_answer.field_dealer_note
    - field.storage.quiz_result_answer.field_dealer_note_2
    - field.storage.quiz_result_answer.field_issue_level
    - field.storage.quiz_result_answer.field_reviewer_answer
    - field.storage.user.field_store_name
  module:
    - options
    - quiz
    - rest
    - serialization
    - text
    - user
    - views_data_export
    - xls_serialization
id: survey_report_detail
label: 'Survey Report Details'
module: views
description: ''
tag: ''
base_table: quiz_result
base_field: result_id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Survey Report'
      fields:
        field_store_name:
          id: field_store_name
          table: user__field_store_name
          field: field_store_name
          relationship: uid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Dealer Name'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed:
          id: changed
          table: quiz_result
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: quiz_result
          entity_field: changed
          plugin_id: field
          label: 'Evaluate date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: d/m/Y
            timezone: Asia/Ho_Chi_Minh
            tooltip:
              date_format: long
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
              description: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_question_category:
          id: field_question_category
          table: quiz_question__field_question_category
          field: field_question_category
          relationship: question_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Categories
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: quiz_question
          field: title
          relationship: question_id
          group_type: group
          admin_label: ''
          entity_type: quiz_question
          entity_field: title
          plugin_id: field
          label: Title
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        body__value:
          id: body__value
          table: quiz_question
          field: body__value
          relationship: question_id
          group_type: group
          admin_label: ''
          entity_type: quiz_question
          entity_field: body
          plugin_id: field
          label: Details
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_dealer_note:
          id: field_dealer_note
          table: quiz_result_answer__field_dealer_note
          field: field_dealer_note
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Dealer Comments'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_reviewer_answer:
          id: field_reviewer_answer
          table: quiz_result_answer__field_reviewer_answer
          field: field_reviewer_answer
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Audit results'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: custom
            format_custom_false: Fail
            format_custom_true: Pass
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_auditor_comment:
          id: field_auditor_comment
          table: quiz_result_answer__field_auditor_comment
          field: field_auditor_comment
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Auditor Comment'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_issue_level:
          id: field_issue_level
          table: quiz_result_answer__field_issue_level
          field: field_issue_level
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Issue Level'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed_1:
          id: changed_1
          table: quiz_result_answer
          field: changed
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          entity_type: quiz_result_answer
          entity_field: changed
          plugin_id: field
          label: 'Date of improvement'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: d/m/Y
            timezone: Asia/Ho_Chi_Minh
            tooltip:
              date_format: long
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
              description: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_dealer_note_2:
          id: field_dealer_note_2
          table: quiz_result_answer__field_dealer_note_2
          field: field_dealer_note_2
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Dealer Recommend'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters: {  }
      style:
        type: table
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: quiz_result
          field: uid
          relationship: none
          group_type: group
          admin_label: User
          entity_type: quiz_result
          entity_field: uid
          plugin_id: standard
          required: false
        reverse__quiz_result_answer__result_id:
          id: reverse__quiz_result_answer__result_id
          table: quiz_result
          field: reverse__quiz_result_answer__result_id
          relationship: none
          group_type: group
          admin_label: 'Quiz result answer'
          entity_type: quiz_result
          plugin_id: standard
          required: false
        question_id:
          id: question_id
          table: quiz_result_answer
          field: question_id
          relationship: reverse__quiz_result_answer__result_id
          group_type: group
          admin_label: 'Quiz question'
          entity_type: quiz_result_answer
          entity_field: question_id
          plugin_id: standard
          required: false
      group_by: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
      tags:
        - 'config:field.storage.quiz_question.field_question_category'
        - 'config:field.storage.quiz_result_answer.field_auditor_comment'
        - 'config:field.storage.quiz_result_answer.field_dealer_note'
        - 'config:field.storage.quiz_result_answer.field_dealer_note_2'
        - 'config:field.storage.quiz_result_answer.field_issue_level'
        - 'config:field.storage.quiz_result_answer.field_reviewer_answer'
        - 'config:field.storage.user.field_store_name'
  data_export_1:
    id: data_export_1
    display_title: 'Data export'
    display_plugin: data_export
    position: 1
    display_options:
      style:
        type: data_export
        options:
          formats:
            xlsx: xlsx
          csv_settings:
            delimiter: ','
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '0'
            use_serializer_encode_only: false
            output_header: true
          xml_settings:
            encoding: UTF-8
            root_node_name: response
            item_node_name: item
            format_output: false
      display_extenders: {  }
      path: survey-export/details
      displays:
        page_1: page_1
        default: '0'
      filename: details.xlsx
      automatic_download: false
      store_in_public_file_directory: null
      custom_redirect_path: false
      redirect_to_display: none
      include_query_params: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
      tags:
        - 'config:field.storage.quiz_question.field_question_category'
        - 'config:field.storage.quiz_result_answer.field_auditor_comment'
        - 'config:field.storage.quiz_result_answer.field_dealer_note'
        - 'config:field.storage.quiz_result_answer.field_dealer_note_2'
        - 'config:field.storage.quiz_result_answer.field_issue_level'
        - 'config:field.storage.quiz_result_answer.field_reviewer_answer'
        - 'config:field.storage.user.field_store_name'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      defaults:
        group_by: true
      display_extenders: {  }
      path: survey-report/details
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
      tags:
        - 'config:field.storage.quiz_question.field_question_category'
        - 'config:field.storage.quiz_result_answer.field_auditor_comment'
        - 'config:field.storage.quiz_result_answer.field_dealer_note'
        - 'config:field.storage.quiz_result_answer.field_dealer_note_2'
        - 'config:field.storage.quiz_result_answer.field_issue_level'
        - 'config:field.storage.quiz_result_answer.field_reviewer_answer'
        - 'config:field.storage.user.field_store_name'
