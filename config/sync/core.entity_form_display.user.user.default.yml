uuid: 97f0be42-a3e0-438b-ac65-d942603976d5
langcode: en
status: true
dependencies:
  config:
    - field.field.user.user.field_country
    - field.field.user.user.field_first_name
    - field.field.user.user.field_last_name
    - field.field.user.user.field_last_password_reset
    - field.field.user.user.field_market_reviewer
    - field.field.user.user.field_password_expiration
    - field.field.user.user.field_pending_expire_sent
    - field.field.user.user.field_picture
    - field.field.user.user.field_store_model
    - field.field.user.user.field_store_name
    - image.style.thumbnail
  module:
    - datetime
    - image
    - user
_core:
  default_config_hash: FaQ9Ptcpxpg30AtiqRDtl_8zbJArHP1LPfug_s59TOA
id: user.user.default
targetEntityType: user
bundle: user
mode: default
content:
  account:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  contact:
    weight: 5
    region: content
  field_country:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_first_name:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_last_name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_last_password_reset:
    type: datetime_default
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_market_reviewer:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_password_expiration:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_picture:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_store_model:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_store_name:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  timezone:
    weight: 6
    region: content
hidden:
  field_pending_expire_sent: true
  langcode: true
  language: true
  path: true
