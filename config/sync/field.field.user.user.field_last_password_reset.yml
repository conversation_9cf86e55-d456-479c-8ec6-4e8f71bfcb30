uuid: aef3abaa-deeb-4309-a0fa-46639b4d9742
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_last_password_reset
  module:
    - datetime
    - user
  enforced:
    module:
      - password_policy
_core:
  default_config_hash: 9KNibQftQSQkxINz3CmNcDC0f6rp2Krw6lkJ2en-G7g
id: user.user.field_last_password_reset
field_name: field_last_password_reset
entity_type: user
bundle: user
label: 'Last Password Reset'
description: ''
required: false
translatable: false
default_value:
  -
    default_date_type: now
    default_date: now
default_value_callback: ''
settings: {  }
field_type: datetime
