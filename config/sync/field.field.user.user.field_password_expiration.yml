uuid: 54883dc7-ea43-434b-ac77-284116ce70c7
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_password_expiration
  module:
    - user
  enforced:
    module:
      - password_policy
_core:
  default_config_hash: 0ivgsjOQ8AUH27JwMzGE7C0VC8M0dGiAZd1JKmC1liQ
id: user.user.field_password_expiration
field_name: field_password_expiration
entity_type: user
bundle: user
label: 'Password Expiration'
description: 'Control whether the user must reset their password. If the password has expired, this field is automatically checked after the execution of Cron.'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: 'Expired Password'
  off_label: 'Non-expired Password'
field_type: boolean
