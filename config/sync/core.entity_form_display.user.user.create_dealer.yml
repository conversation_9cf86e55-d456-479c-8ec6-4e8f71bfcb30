uuid: e7c7cdcd-e8cc-4ba3-aedb-8237a75da2d8
langcode: en
status: true
dependencies:
  config:
    - core.entity_form_mode.user.create_dealer
    - field.field.user.user.field_country
    - field.field.user.user.field_first_name
    - field.field.user.user.field_last_name
    - field.field.user.user.field_last_password_reset
    - field.field.user.user.field_market_reviewer
    - field.field.user.user.field_password_expiration
    - field.field.user.user.field_pending_expire_sent
    - field.field.user.user.field_picture
    - field.field.user.user.field_store_model
    - field.field.user.user.field_store_name
  module:
    - field_group
    - select2
    - user
third_party_settings:
  field_group:
    group_personal_information:
      children:
        - field_picture
        - field_first_name
        - field_last_name
      label: 'Personal Information'
      region: hidden
      parent_name: ''
      weight: 3
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
    group_store_information:
      children:
        - field_country
        - field_store_name
        - field_store_model
        - field_market_reviewer
      label: 'Store Information'
      region: content
      parent_name: ''
      weight: 1
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
    group_basic_information:
      children:
        - account
      label: 'Basic Information'
      region: content
      parent_name: ''
      weight: 0
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
_core:
  default_config_hash: FaQ9Ptcpxpg30AtiqRDtl_8zbJArHP1LPfug_s59TOA
id: user.user.create_dealer
targetEntityType: user
bundle: user
mode: create_dealer
content:
  account:
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  contact:
    weight: 5
    region: content
  field_country:
    type: select2_entity_reference
    weight: 2
    region: content
    settings:
      width: 100%
      autocomplete: false
      match_operator: CONTAINS
      match_limit: 10
    third_party_settings: {  }
  field_market_reviewer:
    type: select2_entity_reference
    weight: 5
    region: content
    settings:
      width: 100%
      autocomplete: false
      match_operator: CONTAINS
      match_limit: 10
    third_party_settings: {  }
  field_store_model:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_store_name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  timezone:
    weight: 6
    region: content
hidden:
  field_first_name: true
  field_last_name: true
  field_last_password_reset: true
  field_password_expiration: true
  field_pending_expire_sent: true
  field_picture: true
  langcode: true
  language: true
  path: true
