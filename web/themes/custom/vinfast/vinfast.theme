<?php

use Dr<PERSON>al\user\Entity\User;
use Dr<PERSON>al\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;

/**
 * @file
 * Functions to support theming in the vinfast theme.
 */

/**
 * Implements hook_preprocess_page().
 */
function vinfast_preprocess_page(array &$variables) {
  if (\Drupal::service('path.matcher')->isFrontPage()) {
    if (in_array('dealer', $variables["user"]->getRoles())) {
      try {
        $quizzes = \Drupal::entityTypeManager()
          ->getStorage('quiz')
          ->loadByProperties(['status' => 1]);
        $user = User::load(\Drupal::currentUser()->id());
        $dealer_type_id = $user->field_store_model->target_id;
        $quiz_result = \Drupal::entityTypeManager()->getStorage('quiz_result');
        $quiz_result_answer = \Drupal::entityTypeManager()->getStorage('quiz_result_answer');
        $data = [];
        foreach ($quizzes as $quiz) {
          $connection = \Drupal::database();
          $query = $connection->select('quiz_question_relationship', 'qqr');
          $query->addField('qqr', 'question_id');
          $query->condition('qqr.quiz_id', $quiz->id());
          $query->condition('qqr.quiz_vid', $quiz->vid->value);
          $question_count = $query->countQuery()->execute()->fetchField();
          $start_date = $quiz->quiz_date->value;
          if (
            $question_count < 1 ||
            (new \DateTime($start_date, new \DateTimeZone('UTC')))
              ->setTimezone(new \DateTimeZone(date_default_timezone_get()))
              ->getTimestamp() > time() ||
            empty($dealer_type_id) ||
            empty($quiz->field_store_model->target_id) ||
            $quiz->field_store_model->target_id != $dealer_type_id
          ) {
            continue;
          }
          $field = $quiz->getFieldDefinition('field_survey_type');
          $options = $field->getSetting('allowed_values');
          $field_label = $options[$quiz->field_survey_type->value] ?? '';
          $q_result = $quiz_result->loadByProperties([
            'uid' => $user->id(),
            'qid' => $quiz->id()
          ]);

          $pending_question = null;
          if(!empty($q_result)) {
            $pending_question = 0;
            $q_result_answers = $quiz_result_answer->loadByProperties([
              'result_id' => reset($q_result)->id(),
            ]);

            foreach ($q_result_answers as $answer) {
              if ($answer->points_awarded->value == null) {
                $pending_question++;
              }
            }
          }

          $end_timestamp = strtotime($quiz->quiz_date->end_value);
          $now_timestamp = time();
          if ($end_timestamp < $now_timestamp) {
            $state = 'Overdue';
          } elseif (!empty($q_result) && reset($q_result)->released->value == "1") {
            $state = 'Completed';
          } elseif (!empty($q_result)) {
            $state = 'Processing';
          } else {
            $state = 'Not Started';
          }
          $data[] = [
            'id' => $quiz->id(),
            'name' => $quiz->label(),
            'type' => [
              'value' => $quiz->field_survey_type->value,
              'label' => $field_label,
            ],
            'start_date' => (new \DateTime($quiz->quiz_date->value, new \DateTimeZone('UTC')))->setTimezone(new \DateTimeZone(date_default_timezone_get()))->format('M d, Y') ?? NULL,
            'end_date' => (new \DateTime($quiz->quiz_date->end_value, new \DateTimeZone('UTC')))->setTimezone(new \DateTimeZone(date_default_timezone_get()))->format('M d, Y') ?? NULL,
            'submitted_date' => !empty($q_result) && reset($q_result)->time_end->value != null ? date('M d, Y', reset($q_result)->time_end->value) : NULL,
            'published' => ($quiz->status->value == 1),
            'total_questions' => $question_count,
            'pending_questions' => $pending_question,
            'survey_state' => $state,
          ];
        }

        $variables['survey_list'] = $data;
        $model_name = "";
        if (isset($user->field_store_model->entity) && $user->field_store_model->entity) {
          $model_name = $user->field_store_model->entity->getName();
        }
        $variables['showroom']['model_name'] = $model_name;
      } catch (\Exception $e) {
        \Drupal::logger('vinfast_theme')->error('API request failed: @error', ['@error' => $e->getMessage()]);
        $variables['survey_list'] = [];
      }
    };

    $variables['#attached']['library'][] = 'vinfast/home';
  }

  $roles = \Drupal::currentUser()->getRoles();
  $variables['roles'] = $roles;
}

/**
 * Implements hook_preprocess_HOOK().
 */
function vinfast_preprocess_vinfast_image_widget(&$variables)
{
  $element = $variables['element'];
  $variables['attributes'] = array('class' => array('vinfast_image-widget', 'js-form-managed-file', 'form-managed-file', 'clearfix'));

  if (!empty($element['fids']['#value'])) {
    $file = reset($element['#files']);
    $file_variables = array(
      'style_name' => $element['#preview_image_style'],
      'uri' => $file->getFileUri(),
    );

    $image_style = \Drupal::entityTypeManager()
      ->getStorage('image_style')
      ->load($element['#preview_image_style']);

    $dealer_style = reset($image_style->getEffects()->getConfiguration());

    $element['preview'] = array(
      '#weight' => -10,
      '#theme' => 'image_style',
      '#width' => $dealer_style["data"]["width"],
      '#height' => $dealer_style["data"]["height"],
      '#style_name' => $file_variables['style_name'],
      '#uri' => $file_variables['uri'],
    );

    $element['remove_button']['#value'] = 'X';
    $element["remove_button"]["#attributes"]['class'][] = 'remove-image';
  }

  $variables['data'] = array();
  foreach (\Drupal\Core\Render\Element::children($element) as $child) {
    $variables['data'][$child] = $element[$child];
  }
}
