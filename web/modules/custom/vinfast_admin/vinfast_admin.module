<?php

/**
 * Implements hook_page_attachments().
 */
function vinfast_admin_page_attachments(array &$attachments) {
  $attachments['#attached']['library'][] = 'vinfast_admin/custom';
}

function vinfast_admin_init() {
  $account = \Drupal::currentUser();

  // Hide errors for all roles except "administrator".
  if (!$account->hasPermission('administer site configuration')) {
    ini_set('display_errors', 0);
    error_reporting(0);
    \Drupal::service('page_cache_kill_switch')->trigger(); // Prevent caching of this error state
  }
}