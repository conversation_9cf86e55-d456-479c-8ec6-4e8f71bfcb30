vinfast_survey.survey_list:
  path: '/survey/{sid}'
  defaults:
    _controller: '\Drupal\vinfast_survey\Controller\SurveyController::index'
    _title_callback: '\Drupal\vinfast_survey\Controller\SurveyController::getTitle'
  requirements:
    _role: 'dealer'
    sid: \d+
  methods: ['GET']

vinfast_survey.reviewer_survey_list:
  path: '/survey/{sId}/survey-result/{srId}'
  defaults:
    _controller: '\Drupal\vinfast_survey\Controller\ReviewerSurveyController::index'
    _title_callback: '\Drupal\vinfast_survey\Controller\ReviewerSurveyController::getTitle'
  requirements:
    _role: 'market_reviewer'
    sid: \d+
    srId: \d+
  methods: ['GET']

vinfast_survey.load_question:
  path: '/survey/{sid}/quiz/question/{qid}'
  defaults:
    _form: '\Drupal\vinfast_survey\Form\QuizAnswerForm'
  requirements:
    _role: 'dealer'
  options:
    _format: 'ajax'
  methods: ['POST']

vinfast_survey.reviewer_evaluation:
  path: '/survey/{sId}/question/{qId}/answer/{aId}/evaluation'
  defaults:
    _form: '\Drupal\vinfast_survey\Form\QuizEvaluationForm'
  requirements:
    _role: 'market_reviewer'
    sId: \d+
    qId: \d+
    aId: \d+
  options:
    _format: 'ajax'
  methods: ['POST']

vinfast_survey.api_survey_submit:
  path: '/api/survey/submit'
  defaults:
    _controller: '\Drupal\vinfast_survey\Controller\SurveySubmitController::submit'
    _format: 'json'
  requirements:
    _role: 'dealer'
  methods: [POST]

vinfast_survey.api_survey_evaluation:
  path: '/api/survey/evaluate'
  defaults:
    _controller: '\Drupal\vinfast_survey\Controller\SurveyEvaluateController::evaluate'
    _format: 'json'
  requirements:
    _role: 'market_reviewer'
  methods: [POST]