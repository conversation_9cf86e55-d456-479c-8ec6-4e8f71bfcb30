<?php

/**
 * Implements hook_views_data().
 */
function vinfast_survey_views_data() {
  $data['views']['table']['group'] = t('Custom Global');
  $data['views']['table']['join'] = [
    // #global is a special flag which allows a table to appear all the time.
    '#global' => [],
  ];

  $data['views']['custom_dealer_grade_field'] = [
    'title' => t('Custom Dealer Grade'),
    'help' => t('Display dealer grade based on score'),
    'field' => [
      'id' => 'custom_dealer_grade_field',
    ],
  ];

  $data['views']['custom_auditor_grade_field'] = [
    'title' => t('Custom Auditor Grade'),
    'help' => t('Display auditor grade based on score'),
    'field' => [
      'id' => 'custom_auditor_grade_field',
    ],
  ];

  $data['views']['custom_report_result_field'] = [
    'title' => t('Custom Report Result'),
    'help' => t('Display Report Result Percent'),
    'field' => [
      'id' => 'custom_report_result_field',
    ],
  ];

  $data['views']['dealer_failed_status_field'] = [
    'title' => t('Dealer Failed Status (Resolved/Unresolved)'),
    'help' => t('Displays whether the dealer failed status is resolved or not'),
    'field' => [
      'id' => 'dealer_failed_status_field',
    ],
  ];

  $data['views']['group_issue_count_field'] = [
    'title' => t('Group Issue Count'),
    'help' => t('Displays the count of each issue level'),
    'field' => [
      'id' => 'group_issue_count_field',
    ],
  ];

  $data['views']['group_review_answer_field'] = [
    'title' => t('Group Review Answer'),
    'help' => t('Displays the count of Pass and Fail review answers for each entity'),
    'field' => [
      'id' => 'group_review_answer_field',
    ],
  ];
  return $data;
}