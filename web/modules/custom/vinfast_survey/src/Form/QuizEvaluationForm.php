<?php

namespace Drupal\vinfast_survey\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\CloseDialogCommand;
use <PERSON><PERSON>al\Core\Ajax\DataCommand;
use Drupal\user\Entity\User;
use Drupal\quiz\Entity\QuizResult;
use Drupal\quiz\Entity\QuizQuestion;
use GuzzleHttp\Exception\ClientException;
use Drupal\Core\Ajax\ReplaceCommand;
use Drupal\Component\Utility\Html;
use Drupal\Component\Utility\Xss;


class QuizEvaluationForm extends FormBase
{

  protected $sId;
  protected $qId;
  protected $aId;
  protected $user;
  protected $quizResult;

  public function getFormId()
  {
    return 'quiz_evaluation_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state, $sId = NULL, $qId = NULL, $aId = NULL)
  {
    $form['#prefix'] = '<div id="vinfast-quiz-form-wrapper">';
    $form['#suffix'] = '</div>';

    $form['captcha_response'] = [
      '#type' => 'hidden',
      '#default_value' => '',
      '#recaptcha_v3' => TRUE,
      '#attributes' => [
        'id' => Html::getUniqueId('recaptcha_v3_token'),
        'class' => ['recaptcha-v3-token'],
        'data-recaptcha-v3-action' => 'captcha',
        'data-recaptcha-v3-site-key' => \Drupal::config('recaptcha_v3.settings')->get('site_key'),
      ],
      '#attached' => [
        'library' => [
          'recaptcha_v3/recaptcha_v3',
        ],
      ],
    ];

    $this->sId = $sId;
    $this->qId = $qId;
    $this->aId = $aId;
    $this->user = User::load(\Drupal::currentUser()->id());
    $is_evaluated = false;
    $quiz_result_answer = NULL;
    $answers = \Drupal::entityTypeManager()
      ->getStorage('quiz_result_answer')
      ->loadByProperties([
        'result_answer_id' => $this->aId,
        'question_id' => $this->qId,
      ]);
    $quiz_result_answer = reset($answers);
    if (!empty($answers)) {
      $quiz_result = QuizResult::load($quiz_result_answer->result_id->target_id);
      $this->quizResult = $quiz_result;
      $is_evaluated = $quiz_result->is_evaluated->value ?? false;
    }

    $form['reviewer_answer'] = [
      '#type' => 'radios',
      '#title' => $this->t('Assessment Status'),
      '#options' => ['1' => 'Pass', '0' => 'Fail'],
      '#default_value' => $quiz_result_answer->field_reviewer_answer->value ?? NULL,
    ];

    $field_definitions = \Drupal::service('entity_field.manager')->getFieldDefinitions('quiz_result_answer', 'short_answer');
    $issue_level_definition = $field_definitions['field_issue_level'];
    $issue_level_options = $issue_level_definition->getSetting('allowed_values');
    $form['issue_level'] = [
      '#type' => 'select',
      '#options' => $issue_level_options,
      '#default_value' => $quiz_result_answer->field_issue_level->value ?? NULL,
      '#attributes' => [
        'class' => ['form-control'],
        'disabled' => $is_evaluated == "1" ?? false,
      ]
    ];

    $file_path = $this->getFilePath($quiz_result_answer, 'field_images');
    $form['image_1'] = $this->buildImageField($file_path);

    $file_2_path = $this->getFilePath($quiz_result_answer, 'field_image_2');
    $form['image_2'] = $this->buildImageField($file_2_path);

    $form['auditor_comment'] = [
      '#type' => 'textarea',
      '#default_value' => $quiz_result_answer->field_auditor_comment->value ?? '',
      '#attributes' => [
        'placeholder' => $this->t("Auditor’s Comment"),
        'rows' => 2,
        'style' => 'width: 100%; padding: 8px 16px',
        'readonly' => $is_evaluated == "1" ?? false,
      ],
    ];

    $quiz = \Drupal::entityTypeManager()->getStorage('quiz')->load($this->sId);

    if ($is_evaluated == false && $quiz->quiz_date->end_value > time()) {
      $form['actions'] = [
        '#type' => 'submit',
        '#value' => $this->t('Submit Evaluate'),
        '#attributes' => [
          'class' => [
            'btn',
            'btn--animated',
            'btn--primary--blue',
            'btn--border--blue',
            'btn-submit-question',
          ],
          'data-input-type' => 'button',
          'disabled' => 'disabled',
        ],
        '#id' => 'submit-evaluate',
        '#ajax' => [
          'wrapper' => 'vinfast-quiz-form-wrapper',
          'callback' => '::ajaxSubmit',
          'event' => 'click',
          'progress' => [
            'type' => 'none',
          ],
        ],
      ];
    }

    $question = QuizQuestion::load($this->qId);
    $quiz_question_relationship = \Drupal::entityTypeManager()
      ->getStorage('quiz_question_relationship')
      ->loadByProperties([
        'question_id' => $this->qId,
        'question_vid' => $question->vid->value,
        'quiz_id' => $quiz->id(),
        'quiz_vid' => $quiz->vid->value,
      ]);
    $form['question'] = ['#markup' => $question->field_question_category->entity->name->value ?? ''];
    $form['question_desc'] = ['#markup' => $question->body->value ?? ''];

    $form['max_score'] = ['#markup' => reset($quiz_question_relationship)->max_score->value ?? ''];

    $form['dealer_note'] = [
      '#markup' => $quiz_result_answer->field_dealer_note->value ?? ''
    ];

    $form['dealer_note_2'] = [
      '#markup' => $quiz_result_answer->field_dealer_note_2->value ?? ''
    ];

    $form['dealer_answer'] = [
      '#markup' => $quiz_result_answer->short_answer->value ?? ''
    ];

    $form['#theme'] = 'vinfast_quiz_evaluation_form';
    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    // No implementation needed for standard submission.
  }

  public function ajaxSubmit(array &$form, FormStateInterface $form_state)
  {
    $response = new AjaxResponse();

    // Validate captcha.
    $captcha = $this->validateCaptcha($form_state);
    if (!$captcha['success']) {
      $selector = '#error-submit';
      $response->addCommand(new ReplaceCommand($selector, $captcha['message']));
      return $response;
    }
    
    $answers = \Drupal::entityTypeManager()
      ->getStorage('quiz_result_answer')
      ->loadByProperties([
        'result_answer_id' => $this->aId,
        'question_id' => $this->qId,
      ]);
    $quiz_result_answer = reset($answers);

    $reviewer_answer = $form_state->getValue('reviewer_answer');
    if ($reviewer_answer === NULL) {
      $selector_captcha = '.recaptcha-v3-token';
      $content_captcha = $form['captcha_response'];
      $command_captcha = new ReplaceCommand($selector_captcha, $content_captcha);
      $response->addCommand($command_captcha);
      return $response;
    }
    $score = 0;
    if ($reviewer_answer === "1") {
      $score =  $form["max_score"]["#markup"];
      $quiz_result_answer->set('field_issue_level', null);
    } else {
      $quiz_result_answer->set('field_issue_level', Xss::filter($form_state->getValue('issue_level')));
    }
    $quiz_result_answer->set('points_awarded', $score);
    $quiz_result_answer->set('is_evaluated', 1);
    $quiz_result_answer->set('field_auditor_comment', Xss::filter($form_state->getValue('auditor_comment')));
    $quiz_result_answer->set('field_reviewer_answer', Xss::filter($reviewer_answer));
    $quiz_result_answer->save();

    $response->addCommand(new CloseDialogCommand('#drupal-modal'));
    $response->addCommand(new DataCommand(
      '#question-' . $this->qId . ' .reviewer-badge',
      [
        'content' => $reviewer_answer === "1" ? 'Pass' : 'Fail',
        'class' => $reviewer_answer === "1" ? 'badge reviewer-badge badge-success' : 'badge reviewer-badge badge-danger',
      ],
      []
    ));

    $response->addCommand(new DataCommand(
      '#question-' . $this->qId . ' .answer-point',
      [
        'content' => $score,
        'class' => ['answer-point', 'point-awarded']
      ],
      []
    ));

    $answers = \Drupal::entityTypeManager()
      ->getStorage('quiz_result_answer')
      ->loadByProperties([
        'result_id' => $this->quizResult->id(),
      ]);

    $evaluate_score = 0;
    $submitted = 0;

    foreach ($answers as $answer) {
      if ($answer->is_evaluated->value == 1) {
        $evaluate_score += $answer->points_awarded->value;
        $submitted++;
      }
    }

    $percentage = ($submitted / count($answers)) * 100;

    $response->addCommand(new DataCommand(
      '.display-data',
      [
        'score' => $evaluate_score,
        'percentage' => $percentage,
        'submitted' => $submitted
      ],
      []
    ));
    return $response;
  }

  private function getFilePath($quiz_result_answer, $field_name)
  {
    if ($quiz_result_answer && $quiz_result_answer->{$field_name}->target_id) {
      $file = \Drupal::entityTypeManager()->getStorage('file')->load($quiz_result_answer->{$field_name}->target_id);
      return $file ? \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri()) : NULL;
    }
    return NULL;
  }

  private function buildImageField($file_path)
  {
    return [
      '#type' => 'managed_file',
      '#title' => $this->t('Upload images'),
      '#default_value' => $file_path,
      '#path_preview' => $file_path,
    ];
  }
  
  private function validateCaptcha(FormStateInterface $form_state)
  {
    try {
      $captcha_response = $form_state->getValue('captcha_response');
      $validate_captcha = \Drupal::service('vinfast_api.validate_recaptcha')->validate($captcha_response);
      if (!$validate_captcha['success']) {
        return [
          'success' => false,
          'message' => '<div id="error-submit" class="form-error mt-2 text-center">Cannot submit the form. Please try again.</div>',
        ];
      }
      return ['success' => true];
    } catch (ClientException $e) {
      return [
        'success' => false,
        'message' => '<div id="error-submit" class="form-error">Cannot submit the form. Please try again.</div>',
      ];
    }
  }
}
