<?php

namespace Drupal\vinfast_survey\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\CloseDialogCommand;
use <PERSON><PERSON>al\Core\Ajax\DataCommand;
use <PERSON><PERSON>al\Core\Ajax\ReplaceCommand;
use <PERSON><PERSON>al\quiz\Entity\QuizResultAnswer;
use Dr<PERSON>al\quiz\Entity\QuizResult;
use Drupal\quiz\Entity\QuizQuestion;
use <PERSON><PERSON>al\user\Entity\User;
use Drupal\Component\Utility\Html;
use Drupal\Component\Utility\Xss;
use GuzzleHttp\Exception\ClientException;

/**
 * Provides a form for answering quiz questions in the VinFast Survey module.
 *
 * This form allows users to submit answers to quiz questions, upload images,
 * and provide optional dealer notes. It supports AJAX submission and integrates
 * with the Quiz module to store results and answers.
 *
 * @file
 * QuizAnswerForm.php
 * 
 * @namespace Drupal\vinfast_survey\Form
 * 
 * @class QuizAnswerForm
 * 
 * @extends Drupal\Core\Form\FormBase
 * 
 * @methods
 * - getFormId(): Returns the unique ID of the form.
 * - buildForm(array $form, FormStateInterface $form_state, $sid = NULL, $qid = NULL): Builds the form structure.
 * - submitForm(array &$form, FormStateInterface $form_state): Handles standard form submission (not implemented).
 * - ajaxSubmit(array &$form, FormStateInterface $form_state): Handles AJAX form submission.
 * - getFilePath($quiz_result_answer, $field_name): Retrieves the file path for a given field in the quiz result answer.
 * - buildImageField($field_name, $default_value, $file_path): Constructs an image upload field with validation and preview.
 * - getOrCreateQuizResult($current_user): Retrieves or creates a quiz result entity for the current user.
 * - getOrCreateQuizResultAnswer($quiz_result): Retrieves or creates a quiz result answer entity for the given quiz result.
 *
 * @properties
 * - $sid: The survey ID.
 * - $qid: The question ID.
 * - $uid: The user ID of the current user.
 *
 * @usage
 * This form is used in the VinFast Survey module to allow users to interact
 * with quiz questions, submit answers, and upload supporting images.
 *
 * @theme vinfast_quiz_answer_form
 * 
 * @ajax
 * - Supports AJAX submission for dynamic updates without page reloads.
 * - Uses CloseDialogCommand to close modal dialogs upon submission.
 *
 * @dependencies
 * - Drupal\Core\Form\FormBase
 * - Drupal\Core\Form\FormStateInterface
 * - Drupal\Core\Ajax\AjaxResponse
 * - Drupal\Core\Ajax\CloseDialogCommand
 * - Drupal\user\Entity\User
 * - Drupal\quiz\Entity\QuizResultAnswer
 * - Drupal\quiz\Entity\QuizResult
 * - Drupal\quiz\Entity\QuizQuestion
 */
class QuizAnswerForm extends FormBase
{

  protected $sid;
  protected $qid;
  protected $user;
  protected $total_question;
  protected $survey_type;

  public function getFormId()
  {
    return 'quiz_answer_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state, $sid = NULL, $qid = NULL)
  {
    $this->sid = $sid;
    $this->qid = $qid;
    $this->user = User::load(\Drupal::currentUser()->id());
    $quiz_result = \Drupal::entityTypeManager()
      ->getStorage('quiz_result')
      ->loadByProperties([
        'uid' => $this->user->id(),
        'qid' => $sid,
      ]);
    $is_release = false;
    $is_evaluated = false;
    $quiz_result_answer = NULL;
    if (!empty($quiz_result)) {
      $answers = \Drupal::entityTypeManager()
        ->getStorage('quiz_result_answer')
        ->loadByProperties([
          'result_id' => reset($quiz_result)->id(),
          'question_id' => $this->qid,
        ]);
      $quiz_result_answer = reset($answers);
      $is_release = reset($quiz_result)->released->value ?? false;
      $is_evaluated = reset($quiz_result)->is_evaluated->value ?? false;
    }

    $quiz = \Drupal::entityTypeManager()->getStorage('quiz')->load($sid);

    $question = QuizQuestion::load($this->qid);
    $this->survey_type = $quiz->field_survey_type->value;
    $quiz_question_relationship = \Drupal::entityTypeManager()
      ->getStorage('quiz_question_relationship')
      ->loadByProperties([
        'question_id' => $qid,
        'question_vid' => $question->vid->value,
        'quiz_id' => $quiz->id(),
        'quiz_vid' => $quiz->vid->value,
      ]);
    $form['question'] = ['#markup' => $question->field_question_category->entity->name->value ?? ''];
    $form['question_desc'] = ['#markup' => $question->body->value ?? ''];

   $form['max_score'] = ['#markup' => reset($quiz_question_relationship)->max_score->value ?? ''];

    $form['captcha_response'] = [
      '#type' => 'hidden',
      '#default_value' => '',
      '#recaptcha_v3' => TRUE,
      '#attributes' => [
        'id' => Html::getUniqueId('recaptcha_v3_token'),
        'class' => ['recaptcha-v3-token'],
        'data-recaptcha-v3-action' => 'captcha',
        'data-recaptcha-v3-site-key' => \Drupal::config('recaptcha_v3.settings')->get('site_key'),
      ],
      '#attached' => [
        'library' => [
          'recaptcha_v3/recaptcha_v3',
        ],
      ],
    ];

    // Round 2
    if ($is_evaluated) {
      $form['dealer_answer'] = [
        '#markup' => $quiz_result_answer->short_answer->value ?? ''
      ];

      $form['dealer_note'] = [
        '#markup' => $quiz_result_answer->field_dealer_note->value ?? ''
      ];

      $file_path = $this->getFilePath($quiz_result_answer, 'field_images');
      $form['image_1'] = $this->buildImageField($file_path);

      $form['auditor_comment'] = [
        '#markup' => $quiz_result_answer->field_auditor_comment->value ?? ''
      ];

      $form['auditor_evaluate'] = [
        '#markup' => $quiz_result_answer->field_reviewer_answer->value ?? ''
      ];

      if ($quiz_result_answer->field_reviewer_answer->value == "0") {
        $file_2_path = $this->getFilePath($quiz_result_answer, 'field_image_2');
        $form['image_2'] = $this->buildImageField($file_2_path);
        $form['dealer_note_2'] = [
          '#type' => 'textarea',
          '#title' => "Dealer's Note",
          '#default_value' => $quiz_result_answer->field_dealer_note_2->value ?? '',
          '#attributes' => [
            'rows' => 2,
            'style' => 'width: 100%; padding: 8px 16px',
            'readonly' => !empty($quiz_result_answer->field_image_2->target_id) ?? false,
            'class' => ['dealer-note']
          ],
          '#required' => $this->survey_type === "sales" ? false : true
        ];

        if (empty($quiz_result_answer->field_image_2->target_id)) {
          $form['actions'] = [
            '#type' => 'submit',
            '#value' => $this->t('Submit Question'),
            '#attributes' => [
              'class' => [
                'btn',
                'btn--animated',
                'btn--primary--blue',
                'btn--border--blue',
                'btn-submit-question',
              ],
              'data-input-type' => 'button',
              'disabled' => 'disabled',
            ],
            '#id' => 'submit-quiz',
            '#ajax' => [
              'wrapper' => 'vinfast-quiz-form-wrapper',
              'callback' => '::ajaxSubmitRoundTwo',
              'event' => 'click',
              'progress' => [
                'type' => 'none',
              ],
            ],
          ];
        }
      }
      $form['#theme'] = 'vinfast_quiz_answer_form_round_two';
      return $form;
    }

    // Round 1
    $form['self_assessment'] = [
      '#type' => 'radios',
      '#title' => $this->t('Self-Assessment'),
      '#options' => ['pass' => 'Pass', 'fail' => 'Fail'],
      '#default_value' => $quiz_result_answer->short_answer->value ?? NULL,
    ];

    $file_path = $this->getFilePath($quiz_result_answer, 'field_images');
    $form['image_1'] = $this->buildImageField($file_path);

    $form['dealer_note'] = [
      '#type' => 'textarea',
      '#title' => "Dealer's Note",
      '#default_value' => $quiz_result_answer->field_dealer_note->value ?? '',
      '#attributes' => [
        'rows' => 2,
        'style' => 'width: 100%; padding: 8px 16px',
        'readonly' => $is_release == "1" ?? false,
        'class' => ['dealer-note']
      ],
      '#required' => $this->survey_type === "sales" ? false : true
    ];

    if ($is_release == false && $quiz->quiz_date->end_value > time()) {
      $form['actions'] = [
        '#type' => 'submit',
        '#value' => $this->t('Submit Question'),
        '#attributes' => [
          'class' => [
            'btn',
            'btn--animated',
            'btn--primary--blue',
            'btn--border--blue',
            'btn-submit-question',
          ],
          'data-input-type' => 'button',
          'disabled' => 'disabled',
        ],
        '#id' => 'submit-quiz',
        '#ajax' => [
          'wrapper' => 'vinfast-quiz-form-wrapper',
          'callback' => '::ajaxSubmitRoundOne',
          'event' => 'click',
          'progress' => [
            'type' => 'none',
          ],
        ],
      ];
    }
    $form['#prefix'] = '<div id="vinfast-quiz-form-wrapper" class="' . $this->survey_type . '">';
    $form['#suffix'] = '</div>';
    $form['#theme'] = 'vinfast_quiz_answer_form_round_one';
    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    // No implementation needed for standard submission.
  }

  public function ajaxSubmitRoundOne(array &$form, FormStateInterface $form_state)
  {
    $response = new AjaxResponse();

    // Validate captcha.
    $captcha = $this->validateCaptcha($form_state);
    if (!$captcha['success']) {
      $selector = '#error-submit';
      $response->addCommand(new ReplaceCommand($selector, $captcha['message']));
      return $response;
    }

    $current_user = \Drupal::currentUser();
    $quiz_result = $this->getOrCreateQuizResult($current_user);

    $self_assessment = $form_state->getValue('self_assessment');
    $dealer_note = $form_state->getValue('dealer_note');
    if ($self_assessment !== NULL) {
      $self_assessment = Xss::filter($self_assessment);
    }
    if ($dealer_note !== NULL) {
      $dealer_note = Xss::filter($dealer_note);
    }

    $score = ($self_assessment === "pass") ? $form["max_score"]["#markup"] ?? 0 : 0;

    $quiz_result_answer = $this->getOrCreateQuizResultAnswer($quiz_result);

    $image_1 = $form_state->getValue('image_1')[0] ?? $quiz_result_answer->field_images->target_id ?? NULL;

    $field_required = $image_1;
    if($this->survey_type === "aftersales") {
      $field_required = $dealer_note;
    }

    if($self_assessment === NULL || $field_required === NULL) {
      $selector_captcha = '.recaptcha-v3-token';
      $content_captcha = $form['captcha_response'];
      $command_captcha = new ReplaceCommand($selector_captcha, $content_captcha);
      $response->addCommand($command_captcha);
      return $response;
    }

    $quiz_result_answer->set('short_answer', $self_assessment);
    $quiz_result_answer->set('is_skipped', 0);
    $quiz_result_answer->set('points_awarded', $score);
    $quiz_result_answer->set('field_dealer_note', $dealer_note);
    $quiz_result_answer->set('field_images', $image_1);
    $quiz_result_answer->save();

    $response->addCommand(new CloseDialogCommand('#drupal-modal'));
    $response->addCommand(new DataCommand(
      '#question-' . $this->qid . ' .badge',
      [
        'content' => $self_assessment === "pass" ? 'Pass' : 'Fail',
        'class' => $self_assessment === "pass" ? 'badge badge-success' : 'badge badge-danger',
      ],
      []
    ));

    $response->addCommand(new DataCommand(
      '#question-' . $this->qid . ' .answer-point',
      [
        'content' => $score,
        'class' => ['answer-point', 'point-awarded']
      ],
      []
    ));

    $answers = \Drupal::entityTypeManager()
      ->getStorage('quiz_result_answer')
      ->loadByProperties([
        'result_id' => $quiz_result->id(),
      ]);

    $dealer_score = 0;
    $submitted = 0;

    foreach ($answers as $answer) {
      if ($answer->points_awarded->value != null) {
        $dealer_score += $answer->points_awarded->value;
        $submitted++;
      }
    }

    $percentage = ($submitted / count($answers)) * 100;

    $response->addCommand(new DataCommand(
      '.display-data',
      [
        'score' => $dealer_score,
        'percentage' => $percentage,
        'submitted' => $submitted
      ],
      []
    ));
    return $response;
  }

  public function ajaxSubmitRoundTwo(array &$form, FormStateInterface $form_state)
  {
    $response = new AjaxResponse();
    // Validate captcha.
    $captcha = $this->validateCaptcha($form_state);
    if (!$captcha['success']) {
      $selector = '#error-submit';
      $response->addCommand(new ReplaceCommand($selector, $captcha['message']));
      return $response;
    }

    $current_user = \Drupal::currentUser();
    $quiz_result = $this->getOrCreateQuizResult($current_user);
    $quiz_result_answer = $this->getOrCreateQuizResultAnswer($quiz_result);
    $dealer_note = Xss::filter($form_state->getValue('dealer_note_2'));
    $image_2 = $form_state->getValue('image_2')[0] ?? $quiz_result_answer->field_image_2->target_id ?? NULL;
    $field_required = $image_2;
    if($this->survey_type === "aftersales") {
      $field_required = $dealer_note;
    }
    if ($field_required === NULL) {
      $selector_captcha = '.recaptcha-v3-token';
      $content_captcha = $form['captcha_response'];
      $command_captcha = new ReplaceCommand($selector_captcha, $content_captcha);
      $response->addCommand($command_captcha);
      return $response;
    }
    $quiz_result_answer->set('field_dealer_note_2', $dealer_note);
    $quiz_result_answer->set('field_image_2', $image_2);
    $quiz_result_answer->save();

    $response->addCommand(new CloseDialogCommand('#drupal-modal'));
    return $response;
  }

  private function getFilePath($quiz_result_answer, $field_name)
  {
    if ($quiz_result_answer && $quiz_result_answer->{$field_name}->target_id) {
      $file = \Drupal::entityTypeManager()->getStorage('file')->load($quiz_result_answer->{$field_name}->target_id);
      return $file ? \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri()) : NULL;
    }
    return NULL;
  }

  private function buildImageField($file_path)
  {
    return [
      '#type' => 'managed_file',
      '#upload_location' => 'gs://',
      '#upload_validators' => [
        'file_validate_extensions' => ['jpg jpeg png'],
        'file_validate_size' => [1024 * 1024 * 3],
      ],
      '#theme' => 'vinfast_image_widget',
      '#preview_image_style' => 'dealer',
      '#multiple' => FALSE,
      '#default_value' => $file_path,
      '#path_preview' => $file_path,
      '#required' => $this->survey_type === "sales" ? true : false
    ];
  }

  private function getOrCreateQuizResult($current_user)
  {
    $quiz_result = \Drupal::entityTypeManager()
      ->getStorage('quiz_result')
      ->loadByProperties([
        'uid' => $current_user->id(),
        'qid' => $this->sid,
      ]);

    if (empty($quiz_result)) {
      $quiz_result = QuizResult::create([
        'type' => 'survey_result',
        'uid' => $current_user->id(),
        'qid' => $this->sid,
        'vid' => $this->sid,
        'created' => time(),
        'time_start' => time(),
        'field_round' => 'round_1',
        'field_store' => NULL,
        'field_reviewer' => NULL,
      ]);
      $quiz_result->save();
    } else {
      $quiz_result = reset($quiz_result);
    }

    return $quiz_result;
  }

  private function getOrCreateQuizResultAnswer($quiz_result)
  {
    $answers = \Drupal::entityTypeManager()
      ->getStorage('quiz_result_answer')
      ->loadByProperties([
        'result_id' => $quiz_result->id(),
        'question_id' => $this->qid,
      ]);

    if (empty($answers)) {
      $quiz_result_answer = QuizResultAnswer::create([
        'result_id' => $quiz_result->id(),
        'question_id' => $this->qid,
      ]);
    } else {
      $quiz_result_answer = reset($answers);
    }

    return $quiz_result_answer;
  }

  private function validateCaptcha(FormStateInterface $form_state)
  {
    try {
      $captcha_response = $form_state->getValue('captcha_response');
      $validate_captcha = \Drupal::service('vinfast_api.validate_recaptcha')->validate($captcha_response);
      if (!$validate_captcha['success']) {
        return [
          'success' => false,
          'message' => '<div id="error-submit" class="form-error mt-2 text-center">Cannot submit the form. Please try again.</div>',
        ];
      }
      return ['success' => true];
    } catch (ClientException $e) {
      return [
        'success' => false,
        'message' => '<div id="error-submit" class="form-error">Cannot submit the form. Please try again.</div>',
      ];
    }
  }
}
