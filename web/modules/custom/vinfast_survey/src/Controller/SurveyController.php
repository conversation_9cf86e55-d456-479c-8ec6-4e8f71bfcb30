<?php

namespace Drupal\vinfast_survey\Controller;

use Symfony\Component\HttpFoundation\Request;
use Drupal\Core\Controller\ControllerBase;
use Drupal\user\Entity\User;
use Drupal\quiz\Entity\QuizQuestion;
use Drupal\taxonomy\Entity\Term;

class SurveyController extends ControllerBase {

    protected $sId;
    protected $srId;
    protected $user;
    protected $quiz;

    public function index(Request $request) {
        $this->sId = $request->get('sid');
        $this->user = User::load(\Drupal::currentUser()->id());
        $this->quiz = $this->entityTypeManager()->getStorage('quiz')->load($this->sId);
        if($this->quiz == null) {
            return [
                '#markup' => $this->t('Quiz not found'),
            ];
        }
        $result = $this->buildSurveyData();
        $build = [
            '#theme' => 'survey_list',
            '#survey' => $result,
            '#attached' => [
                'library' => ['vinfast_survey/survey_detail', 'vinfast_survey/quiz_form'],
            ]
        ];

        return $build;
    }

    public function getTitle(Request $request) {
        $quiz = $this->entityTypeManager()->getStorage('quiz')->load($request->get('sid'));

        return $quiz != null ? $quiz->label() : '';
    }

    private function buildSurveyData()
    {
        $user = $this->user;
        $quiz = $this->quiz;
        $auditor = User::load($user->field_market_reviewer->target_id);
        $auditor_name = $auditor ? $auditor->getDisplayName() : '';
        $auditor_email = $auditor ? $auditor->mail->value : '';
        // Get all question IDs and count.
        $connection = \Drupal::database();
        $query = $connection->select('quiz_question_relationship', 'qqr')
            ->fields('qqr', ['question_id'])
            ->condition('qqr.quiz_id', $quiz->id())
            ->condition('qqr.quiz_vid', $quiz->vid->value);
        $question_ids = $query->execute()->fetchCol();
        $question_count = count($question_ids);

        // Load all questions at once.
        $questions = QuizQuestion::loadMultiple($question_ids);

        // Survey type label.
        $field = $quiz->getFieldDefinition('field_survey_type');
        $options = $field->getSetting('allowed_values');
        $field_label = $options[$quiz->field_survey_type->value] ?? NULL;

        // Load quiz result and answer storage.
        $quiz_result = \Drupal::entityTypeManager()
            ->getStorage('quiz_result')
            ->loadByProperties([
                'uid' => $user->id(),
                'qid' => $quiz->id(),
            ]);
        $quiz_result_entity = reset($quiz_result);
        $quiz_result_answer_storage = \Drupal::entityTypeManager()->getStorage('quiz_result_answer');

        // Dates.
        $start_date = !empty($quiz->quiz_date->value)
            ? (new \DateTime($quiz->quiz_date->value, new \DateTimeZone('UTC')))->setTimezone(new \DateTimeZone(date_default_timezone_get()) ?: null)
            : null;
        $end_date = !empty($quiz->quiz_date->end_value)
            ? (new \DateTime($quiz->quiz_date->end_value, new \DateTimeZone('UTC')))->setTimezone(new \DateTimeZone(date_default_timezone_get()) ?: null)
            : null;

        $question_data = [];
        $submitted_question = 0;
        $dealer_score = 0;
        $max_score = 0;
        $dealer_passed = 0;
        $auditor_passed = 0;
        foreach ($questions as $q) {
            // Question category term.
            $qId = $q->field_question_category->target_id ?? null;
            $qTerm = $qId ? Term::load($qId) : null;
            $qName = $qTerm ? $qTerm->label() : null;

            // Answer for this question.
            $answer = [];
            if ($quiz_result_entity) {
                $answer = $quiz_result_answer_storage->loadByProperties([
                    'result_id' => $quiz_result_entity->id(),
                    'question_id' => $q->id(),
                ]);
            }
            $answer_entity = reset($answer);
            if($answer_entity->points_awarded->value != null ) {
                $submitted_question += 1;
                $dealer_score += $answer_entity->points_awarded->value;
                if($answer_entity->short_answer->value === "pass") {
                    $dealer_passed += 1;
                }
                if($answer_entity->field_reviewer_answer->value === "1") {
                    $auditor_passed += 1;
                }
            }
            // Parent terms hierarchy.
            $parentTerms = [];
            $currentTerm = $qTerm;
            while ($currentTerm && $currentTerm->parent->target_id) {
                $parentTerm = Term::load($currentTerm->parent->target_id);
                if ($parentTerm) {
                    $parentTerms[] = [
                        'id' => $parentTerm->id(),
                        'name' => $parentTerm->label(),
                    ];
                    $currentTerm = $parentTerm;
                } else {
                    break;
                }
            }
            $parentTerms = array_reverse($parentTerms);

            // Store model term.
            $sId = $q->field_store_model->target_id ?? null;
            $sTerm = $sId ? Term::load($sId) : null;
            $sName = $sTerm ? $sTerm->label() : null;

            // Relationship.
            $relationship = \Drupal::entityTypeManager()
                ->getStorage('quiz_question_relationship')
                ->loadByProperties([
                    'question_id' => $q->id(),
                    'question_vid' => $q->id(),
                    'quiz_id' => $quiz->id(),
                    'quiz_vid' => $quiz->vid->value,
                ]);
            $relationship = reset($relationship);
            $max_score += $relationship->max_score->value ?? 0;

            $question_data[] = [
                'id' => $q->id(),
                'title' => $q->label(),
                'type' => $q->bundle(),
                'body' => $q->body->value ?? null,
                'score' => $relationship->max_score->value ?? 0,
                'published' => ($q->status->value == 1),
                'field_question_category' => [
                    'id' => $qId,
                    'name' => $qName,
                ],
                'field_store_model' => [
                    'id' => $sId,
                    'name' => $sName,
                ],
                'points_awarded' => $answer_entity->points_awarded->value ?? 0,
                'short_answer' => $answer_entity->short_answer->value ?? null,
                'parent_terms' => $parentTerms,
                'weight' => $relationship->weight->value ?? 0,
                'issue_level' => $answer_entity->field_issue_level->value ?? null,
                'reviewer_answer' => $answer_entity->field_reviewer_answer->value ?? null,
            ];
        }

        // Group questions by parent terms.
        $grouped = [];
        foreach ($question_data as $question) {
            $parent_0 = $question['parent_terms'][0] ?? ['id' => 0, 'name' => 'Ungrouped'];
            $parent_1 = $question['parent_terms'][1] ?? ['id' => 0, 'name' => 'Ungrouped'];
            $group_id = $parent_0['id'];
            $sub_id = $parent_1['id'];

            if (!isset($grouped[$group_id])) {
                $grouped[$group_id] = [
                    'parent_terms' => $parent_0,
                    'children' => [],
                ];
            }
            if (!isset($grouped[$group_id]['children'][$sub_id])) {
                $grouped[$group_id]['children'][$sub_id] = [
                    'parent_terms_child' => $parent_1,
                    'questions' => [],
                ];
            }
            $grouped[$group_id]['children'][$sub_id]['questions'][] = $question;
        }

        return [
            'id' => $quiz->id(),
            'is_released' => !empty($quiz_result) && $quiz_result_entity->released->value == "1",
            'is_evaluated' => !empty($quiz_result) && $quiz_result_entity->is_evaluated->value == '1',
            'name' => $quiz->label(),
            'showroom_model' => $user->field_store_model->entity->getName() ?? "",
            'type' => [
                'value' => $quiz->field_survey_type->value,
                'label' => $field_label,
            ],
            'auditor' => [
                'name' => $auditor_name,
                'email' => $auditor_email,
            ],
            'start_date' => $start_date ? $start_date->format('M Y') : null,
            'end_date' => $end_date ? $end_date->format('M d, Y') : null,
            'end_date_timestamp' => $end_date ? $end_date->format('U') : null,
            'description' => $quiz->body->value ?? null,
            'published' => ($quiz->status->value == 1),
            'grouped_questions' => array_values($grouped),
            'round_1' => [
                'score' => $dealer_score,
                'max_score' => $max_score,
                'total_questions' => $question_count,
                'submitted_questions' => $submitted_question,
            ],
            'round_2' => [
                'dealer_score' => !empty($quiz_result) ? $quiz_result_entity->field_dealer_score->value : 0,
                'max_score' => $max_score,
                'auditor_score' => !empty($quiz_result) ? $quiz_result_entity->score->value : 0,
                'dealer_grade' => !empty($quiz_result) ? $quiz_result_entity->field_dealer_grade->value : '',
                'auditor_grade' => !empty($quiz_result) ? $quiz_result_entity->field_auditor_grade->value : '',
                'dealer_passed' => $dealer_passed,
                'auditor_passed' => $auditor_passed,
            ],
        ];
    }
}