<?php

namespace Drupal\vinfast_survey\Plugin\views\field;

use <PERSON><PERSON>al\views\Annotation\ViewsField;
use <PERSON><PERSON>al\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;
use Drupal\vinfast_survey\Helper\QuizResultAnswerTrait;

/**
 * Provides a field that renders all group issue counts in one row.
 *
 * @ViewsField("group_review_answer_field")
 */
class GroupReviewAnswerField extends FieldPluginBase
{
  use QuizResultAnswerTrait;

  /**
   * {@inheritdoc}
   */
  public function defineOptions()
  {
    $options = parent::defineOptions();
    $options['review_value'] = ['default' => 1]; // 1: Pass, 0: Fail
    return $options;
  }

  /**
   * {@inheritdoc}
   */
  public function buildOptionsForm(&$form, \Drupal\Core\Form\FormStateInterface $form_state)
  {
    parent::buildOptionsForm($form, $form_state);
    $form['review_value'] = [
      '#type' => 'select',
      '#title' => $this->t('Review Answer'),
      '#options' => [
        1 => $this->t('Pass'),
        0 => $this->t('Fail'),
      ],
      '#default_value' => $this->options['review_value'],
      '#description' => $this->t('Select Pass or Fail to count.'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function usesGroupBy()
  {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function query()
  {
    // Do nothing -- to override the parent query.
  }

  public function render(ResultRow $values)
  {
    $count = 0;
    $entity = $values->_entity;
    $quiz_result_id = $entity->id();

    // Get the value to count: 1 (Pass) or 0 (Fail)
    $review_value = $this->options['review_value'] ?? 1; // 1: Pass, 0: Fail

    $connection = \Drupal::database();

    // Get all result_answer_id by quiz_result_id
    $answer_ids = $this->getAnswerIdsByQuizResult($quiz_result_id);

    if (!empty($answer_ids)) {
      // Count the number of review_answer = $review_value
      $count = (int) $connection->select('quiz_result_answer__field_reviewer_answer', 'f')
        ->condition('entity_id', $answer_ids, 'IN')
        ->condition('field_reviewer_answer_value', $review_value)
        ->countQuery()
        ->execute()
        ->fetchField();
    }

    return $count;
  }

  public function getLabel()
  {
    return $this->t('Group Review Answer');
  }
}
