<?php

namespace Drupal\vinfast_survey\Plugin\views\field;

use <PERSON><PERSON>al\views\Annotation\ViewsField;
use Drupal\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;
use Drupal\vinfast_survey\Helper\QuizResultAnswerTrait;

/**
 * Provides a field that renders all group issue counts in one row.
 *
 * @ViewsField("custom_report_result_field")
 */
class CustomReportResultField extends FieldPluginBase
{
  use QuizResultAnswerTrait;

  /**
   * {@inheritdoc}
   */
  public function defineOptions()
  {
    $options = parent::defineOptions();
    return $options;
  }

  /**
   * {@inheritdoc}
   */
  public function usesGroupBy()
  {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function query()
  {
    // Do nothing -- to override the parent query.
  }

  public function render(ResultRow $values)
  {
    $entity = $values->_entity;
    $quiz_result_id = $entity->id();

    $connection = \Drupal::database();

    // Get all result_answer_id by quiz_result_id
    $answer_ids = $this->getAnswerIdsByQuizResult($quiz_result_id);

    $total = count($answer_ids);
    $pass = 0;

    if ($total > 0) {
      // Count the number of Pass (review_answer = 1)
      $pass = (int) $connection->select('quiz_result_answer__field_reviewer_answer', 'f')
        ->condition('entity_id', $answer_ids, 'IN')
        ->condition('field_reviewer_answer_value', 1)
        ->countQuery()
        ->execute()
        ->fetchField();

      $percent = round(($pass / $total) * 100, 2);
      return $percent;
    }

    return 0;
  }

  public function getLabel()
  {
    return $this->t('Custom Report Result');
  }
}
