<?php

namespace Drupal\vinfast_survey\Plugin\views\field;

use <PERSON><PERSON>al\views\Annotation\ViewsField;
use Drupal\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;
use Drupal\vinfast_survey\Helper\QuizResultAnswerTrait;

/**
 * Provides a field that renders all group issue counts in one row.
 *
 * @ViewsField("dealer_failed_status_field")
 */
class DealerFailedStatusField extends FieldPluginBase
{
  use QuizResultAnswerTrait;

  /**
   * {@inheritdoc}
   */
  public function defineOptions()
  {
    $options = parent::defineOptions();
    $options['resolve_status'] = ['default' => 'all']; // all, resolved, unresolved
    return $options;
  }

  /**
   * {@inheritdoc}
   */
  public function buildOptionsForm(&$form, \Drupal\Core\Form\FormStateInterface $form_state)
  {
    parent::buildOptionsForm($form, $form_state);
    $form['resolve_status'] = [
      '#type' => 'select',
      '#title' => $this->t('Resolve status'),
      '#options' => [
        'all' => $this->t('All'),
        'resolved' => $this->t('Resolved'),
        'unresolved' => $this->t('Unresolved'),
      ],
      '#default_value' => $this->options['resolve_status'],
      '#description' => $this->t('Filter by resolve status.'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function usesGroupBy()
  {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function query()
  {
    // Do nothing -- to override the parent query.
  }

  public function render(ResultRow $values)
  {
    $entity = $values->_entity;
    $quiz_result_id = $entity->id();
    $resolve_status = $this->options['resolve_status'] ?? 'all';

    $connection = \Drupal::database();
    $answer_ids = $this->getAnswerIdsByQuizResult($quiz_result_id);

    $count = 0;
    if (!empty($answer_ids)) {
      // Only get answer_ids with Fail (field_reviewer_answer_value = 0)
      $fail_ids = $connection->select('quiz_result_answer__field_reviewer_answer', 'f')
        ->fields('f', ['entity_id'])
        ->condition('entity_id', $answer_ids, 'IN')
        ->condition('field_reviewer_answer_value', 0)
        ->execute()
        ->fetchCol();

      if (!empty($fail_ids)) {
        // Get resolved ids (entity_id exists in image_2 or dealer_note_2)
        $image_ids = $connection->select('quiz_result_answer__field_image_2', 'img')
          ->fields('img', ['entity_id'])
          ->condition('entity_id', $fail_ids, 'IN')
          ->execute()
          ->fetchCol();
        $note_ids = $connection->select('quiz_result_answer__field_dealer_note_2', 'note')
          ->fields('note', ['entity_id'])
          ->condition('entity_id', $fail_ids, 'IN')
          ->execute()
          ->fetchCol();
        $resolved_ids = array_unique(array_merge($image_ids, $note_ids));

        if ($resolve_status === 'resolved') {
          $count = count($resolved_ids);
        } elseif ($resolve_status === 'unresolved') {
          $count = count(array_diff($fail_ids, $resolved_ids));
        } else { // all failed
          $count = count($fail_ids);
        }
      }
    }

    return $count;
  }

  public function getLabel()
  {
    return $this->t('Dealer Failed Status (Resolved/Unresolved)');
  }
}
