<?php

namespace Drupal\vinfast_survey\Plugin\views\field;

use <PERSON><PERSON>al\views\Annotation\ViewsField;
use <PERSON><PERSON>al\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;
use Drupal\vinfast_survey\Helper\QuizResultAnswerTrait;

/**
 * Provides a field that renders all group issue counts in one row.
 *
 * @ViewsField("group_issue_count_field")
 */
class GroupIssueCountField extends FieldPluginBase
{
  use QuizResultAnswerTrait;

  /**
   * {@inheritdoc}
   */
  public function defineOptions()
  {
    $options = parent::defineOptions();
    $options['issue_level'] = ['default' => 'level_1_issue'];
    return $options;
  }

  /**
   * {@inheritdoc}
   */
  public function buildOptionsForm(&$form, \Drupal\Core\Form\FormStateInterface $form_state)
  {
    parent::buildOptionsForm($form, $form_state);
    $form['issue_level'] = [
      '#type' => 'select',
      '#title' => $this->t('Issue level'),
      '#options' => [
        'level_1_issue' => $this->t('Level 1'),
        'level_2_issue' => $this->t('Level 2'),
        'level_3_issue' => $this->t('Level 3'),
        'level_4_issue' => $this->t('Level 4'),
      ],
      '#default_value' => $this->options['issue_level'],
      '#description' => $this->t('Select which issue level to count.'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function usesGroupBy()
  {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function query()
  {
    // Do nothing -- to override the parent query.
  }

  public function render(ResultRow $values)
  {
    $count = 0;
    $entity = $values->_entity;
    $quiz_result_id = $entity->id();
    $level_to_count = $this->options['issue_level'] ?? 'level_1_issue';

    $connection = \Drupal::database();

    // Get all result_answer_id by quiz_result_id
    $answer_ids = $this->getAnswerIdsByQuizResult($quiz_result_id);

    if (!empty($answer_ids)) {
      // Count the number of issues by level for the list of answer_ids
      $count = (int) $connection->select('quiz_result_answer__field_issue_level', 'f')
        ->condition('entity_id', $answer_ids, 'IN')
        ->condition('field_issue_level_value', $level_to_count)
        ->countQuery()
        ->execute()
        ->fetchField();
    }

    return $count;
  }

  public function getLabel()
  {
    return $this->t('Group Issue Count');
  }
}
