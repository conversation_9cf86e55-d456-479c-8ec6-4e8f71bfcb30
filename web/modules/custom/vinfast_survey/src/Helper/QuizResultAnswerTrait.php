<?php

namespace Drupal\vinfast_survey\Helper;

trait QuizResultAnswerTrait
{
  protected function getAnswerIdsByQuizResult($quiz_result_id)
  {
    static $cache = [];
    if (!isset($cache[$quiz_result_id])) {
      $connection = \Drupal::database();
      $cache[$quiz_result_id] = $connection->select('quiz_result_answer', 'a')
        ->fields('a', ['result_answer_id'])
        ->condition('result_id', $quiz_result_id)
        ->execute()
        ->fetchCol();
    }
    return $cache[$quiz_result_id];
  }
}
